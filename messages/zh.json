{"common": {"loading": "加载中…", "error": "发生错误", "success": "成功", "cancel": "取消", "confirm": "确认", "save": "保存", "edit": "编辑", "delete": "删除", "search": "搜索", "filter": "筛选", "sort": "排序", "next": "下一页", "previous": "上一页", "close": "关闭", "open": "打开"}, "navigation": {"home": "首页", "about": "关于我们", "contact": "联系我们", "services": "服务", "products": "产品", "blog": "博客", "login": "登录", "logout": "退出", "profile": "个人资料", "settings": "设置"}, "theme": {"toggle": "切换主题", "light": "明亮模式", "dark": "暗黑模式", "system": "系统模式"}, "language": {"toggle": "切换语言", "english": "英语", "chinese": "中文", "switching": "正在切换语言…", "switchSuccess": "语言切换成功", "switchError": "语言切换失败", "fallbackWarning": "部分内容可能无法以您选择的语言显示"}, "home": {"title": "Tucsenberg Web Frontier", "subtitle": "现代化B2B企业级Web平台", "description": "基于Next.js 15、React 19和TypeScript构建的现代化B2B企业级Web平台。", "getStarted": "开始使用", "learnMore": "了解更多", "features": {"title": "特性", "performance": {"title": "高性能", "description": "采用现代Web技术优化速度和效率"}, "scalable": {"title": "可扩展架构", "description": "随着您的业务需求和要求而增长"}, "secure": {"title": "企业级安全", "description": "行业标准的安全实践和合规性"}}}, "themeDemo": {"title": "主题系统演示", "description": "测试主题切换和中英文字体混排效果", "chineseTest": "中文字体测试", "englishTest": "英文字体测试", "chineseDescription": "这是中文字体测试内容，使用了PingFang SC字体回退策略。现代化B2B企业级设计风格展示。", "englishDescription": "这是英文字体测试内容，使用Geist Sans字体。现代化B2B企业级设计风格展示。", "chineseInput": "中文输入框", "englishInput": "英文输入框", "chinesePlaceholder": "请输入中文内容", "englishPlaceholder": "请输入英文内容", "primaryButton": "主要按钮", "secondaryButton": "次要按钮", "outlineButton": "轮廓按钮"}, "instructions": {"getStarted": "开始编辑", "saveChanges": "保存并立即查看您的更改"}, "actions": {"deployNow": "立即部署", "readDocs": "阅读文档"}, "footer": {"learn": "学习", "examples": "示例", "goToNextjs": "访问nextjs.org"}, "formatting": {"date": {"today": "今天", "yesterday": "昨天", "tomorrow": "明天", "lastUpdated": "最后更新于 {date}", "publishedOn": "发布于 {date}"}, "number": {"currency": "¥{amount}", "percentage": "{value}％", "fileSize": "{size}{unit}"}, "plurals": {"items": {"zero": "暂无项目", "one": "1个项目", "other": "{count}个项目"}, "users": {"zero": "暂无在线用户", "one": "1位用户在线", "other": "{count}位用户在线"}, "notifications": {"zero": "暂无新通知", "one": "1条新通知", "other": "{count}条新通知"}}}, "errors": {"notFound": "页面未找到", "serverError": "服务器内部错误", "networkError": "网络连接错误", "validationError": "请检查您的输入", "unauthorized": "您无权访问此页面", "forbidden": "禁止访问此资源", "timeout": "请求超时，请重试", "generic": "出现了一些问题，请稍后重试", "translationMissing": "翻译不可用", "loadingFailed": "内容加载失败"}, "accessibility": {"skipToContent": "跳转到主要内容", "openMenu": "打开导航菜单", "closeMenu": "关闭导航菜单", "loading": "内容正在加载", "error": "发生了错误", "languageSelector": "选择语言", "themeSelector": "选择主题"}, "seo": {"title": "图森堡网络前沿", "description": "基于Next.js 15、React 19和TypeScript的现代化B2B企业级网络平台", "siteName": "图森堡网络前沿", "keywords": "Next.js, React, TypeScript, B2B, 企业级, 网络平台"}, "structured-data": {"organization": {"name": "图森堡网络前沿", "description": "现代化B2B企业级网络平台", "phone": "+86-************", "social": {"twitter": "https://twitter.com/tucsenberg", "linkedin": "https://linkedin.com/company/tucsenberg", "github": "https://github.com/tucsenberg"}}, "website": {"name": "图森堡网络前沿", "description": "基于Next.js 15的现代化B2B企业级网络平台"}, "article": {"defaultTitle": "文章", "defaultDescription": "文章描述", "defaultAuthor": "图森堡团队"}, "product": {"defaultName": "产品", "defaultDescription": "产品描述"}}}
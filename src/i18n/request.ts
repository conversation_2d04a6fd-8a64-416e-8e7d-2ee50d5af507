import { getRequestConfig } from 'next-intl/server';

import {
  getCachedMessages,
  I18nPerformanceMonitor,
  TranslationCache,
} from '@/lib/i18n-performance';

import { routing } from './routing';

// 辅助函数：获取格式配置
function getFormats(locale: string) {
  return {
    dateTime: {
      short: {
        day: 'numeric' as const,
        month: 'short' as const,
        year: 'numeric' as const,
      },
      long: {
        day: 'numeric' as const,
        month: 'long' as const,
        year: 'numeric' as const,
        weekday: 'long' as const,
      },
    },
    number: {
      precise: {
        maximumFractionDigits: 5,
      },
      currency: {
        style: 'currency' as const,
        currency: locale === 'zh' ? 'CNY' : 'USD',
      },
      percentage: {
        style: 'percent' as const,
        minimumFractionDigits: 1,
      },
    },
    list: {
      enumeration: {
        style: 'long' as const,
        type: 'conjunction' as const,
      },
    },
  };
}

// 辅助函数：处理缓存性能监控
function handleCacheMetrics(locale: string, loadTime: number) {
  I18nPerformanceMonitor.recordLoadTime(loadTime);

  const cache = TranslationCache.getInstance();
  const cached = cache.get(`messages-${locale}`);
  if (cached) {
    I18nPerformanceMonitor.recordCacheHit();
  } else {
    I18nPerformanceMonitor.recordCacheMiss();
  }

  return Boolean(cached);
}

// 辅助函数：创建成功响应
function createSuccessResponse(
  locale: string,
  messages: Record<string, unknown>,
  loadTime: number,
  cacheUsed: boolean,
) {
  return {
    locale,
    messages,
    timeZone: locale === 'zh' ? 'Asia/Shanghai' : 'UTC',
    now: new Date(),
    formats: getFormats(locale),
    metadata: {
      loadTime,
      cacheUsed,
      timestamp: Date.now(),
    },
  };
}

// 辅助函数：创建错误回退响应
async function createFallbackResponse(locale: string, startTime: number) {
  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default,
    timeZone: locale === 'zh' ? 'Asia/Shanghai' : 'UTC',
    now: new Date(),
    formats: getFormats(locale),
    metadata: {
      loadTime: performance.now() - startTime,
      cacheUsed: false,
      error: true,
      timestamp: Date.now(),
    },
  };
}

export default getRequestConfig(async ({ requestLocale }) => {
  const startTime = performance.now();
  let locale = await requestLocale;

  // 确保使用有效的语言
  if (!locale || !routing.locales.includes(locale as 'en' | 'zh')) {
    locale = routing.defaultLocale;
  }

  try {
    const messages = await getCachedMessages(locale);
    const loadTime = performance.now() - startTime;
    const cacheUsed = handleCacheMetrics(locale, loadTime);

    return createSuccessResponse(locale, messages, loadTime, cacheUsed);
  } catch {
    I18nPerformanceMonitor.recordError();
    return createFallbackResponse(locale, startTime);
  }
});

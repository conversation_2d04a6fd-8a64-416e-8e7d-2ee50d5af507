import { Metadata } from 'next';

import { getTranslations } from 'next-intl/server';

import { routing } from '@/i18n/routing';

export type Locale = 'en' | 'zh';

interface SEOConfig {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  type?: 'website' | 'article' | 'product';
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  section?: string;
}

/**
 * 获取本地化路径
 */
function getLocalizedPath(locale: Locale, namespace: string): string {
  if (namespace === 'home') {
    return '';
  }

  // 简化的路径映射，只处理主要页面
  if (locale === 'zh') {
    if (namespace === 'about') return '/guanyu';
    if (namespace === 'contact') return '/lianxi';
  }

  // 默认返回英文路径
  return `/${namespace}`;
}

/**
 * 获取规范化URL
 */
function getCanonicalUrl(locale: Locale, namespace: string): string {
  const baseUrl = process.env['SITE_URL'] || 'https://tucsenberg.com';
  const localizedPath = getLocalizedPath(locale, namespace);

  if (locale === 'en') {
    return `${baseUrl}${localizedPath}`;
  }
  return `${baseUrl}/${locale}${localizedPath}`;
}

/**
 * 生成本地化元数据
 */
export async function generateLocalizedMetadata(
  locale: Locale,
  namespace: string,
  config: SEOConfig = {},
): Promise<Metadata> {
  // 使用原始的getTranslations，缓存已在底层实现
  const t = await getTranslations({ locale, namespace: 'seo' });

  const title =
    config.title || t('title', { defaultValue: 'Tucsenberg Web Frontier' });
  const description =
    config.description ||
    t('description', {
      defaultValue: 'Modern B2B Enterprise Web Platform with Next.js 15',
    });
  const siteName = t('siteName', { defaultValue: 'Tucsenberg Web Frontier' });

  const metadata: Metadata = {
    title,
    description,
    keywords: config.keywords,

    // Open Graph本地化
    openGraph: {
      title,
      description,
      siteName,
      locale,
      type: (config.type === 'product' ? 'website' : config.type) || 'website',
      images: config.image ? [{ url: config.image }] : undefined,
      publishedTime: config.publishedTime,
      modifiedTime: config.modifiedTime,
      authors: config.authors,
      section: config.section,
    },

    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: config.image ? [config.image] : undefined,
    },

    // hreflang和canonical链接
    alternates: {
      canonical: getCanonicalUrl(locale, namespace),
      languages: Object.fromEntries(
        routing.locales.map((loc) => [
          loc,
          getCanonicalUrl(loc as Locale, namespace),
        ]),
      ),
    },

    // 其他元数据
    robots: {
      index: true,
      follow: true,
      googleBot: {
        'index': true,
        'follow': true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

    // 验证标签
    verification: {
      google: process.env['GOOGLE_SITE_VERIFICATION'],
      yandex: process.env['YANDEX_VERIFICATION'],
    },
  };

  return metadata;
}

/**
 * 生成页面特定的SEO配置
 */
export function createPageSEOConfig(
  pageType: 'home' | 'about' | 'contact' | 'blog' | 'product',
  customConfig: Partial<SEOConfig> = {},
): SEOConfig {
  const baseConfigs = {
    home: {
      type: 'website' as const,
      keywords: ['Next.js', 'React', 'TypeScript', 'B2B', 'Enterprise'],
    },
    about: {
      type: 'website' as const,
      keywords: ['About', 'Company', 'Team', 'Enterprise'],
    },
    contact: {
      type: 'website' as const,
      keywords: ['Contact', 'Support', 'Business'],
    },
    blog: {
      type: 'article' as const,
      keywords: ['Blog', 'Articles', 'Technology', 'Insights'],
    },
    product: {
      type: 'product' as const,
      keywords: ['Product', 'Solution', 'Enterprise', 'B2B'],
    },
  };

  let baseConfig;
  switch (pageType) {
    case 'home':
      baseConfig = baseConfigs.home;
      break;
    case 'about':
      baseConfig = baseConfigs.about;
      break;
    case 'contact':
      baseConfig = baseConfigs.contact;
      break;
    case 'blog':
      baseConfig = baseConfigs.blog;
      break;
    case 'product':
      baseConfig = baseConfigs.product;
      break;
    default:
      baseConfig = baseConfigs.home;
  }

  return {
    ...baseConfig,
    ...customConfig,
  };
}

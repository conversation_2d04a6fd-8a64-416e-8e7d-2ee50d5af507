/** @type {import('next-sitemap').IConfig} */

// 导入统一配置
const { getSitemapConfig } = require('./src/config/paths.ts');

// 获取统一的sitemap配置
const sitemapConfig = getSitemapConfig();

module.exports = {
  siteUrl: sitemapConfig.baseUrl,
  generateRobotsTxt: true,
  exclude: ['/admin/*', '/api/*', '/server-sitemap.xml'],

  // next-intl多语言支持 - 使用统一配置
  alternateRefs: [
    {
      href: sitemapConfig.baseUrl,
      hreflang: 'en',
    },
    {
      href: `${sitemapConfig.baseUrl}/zh`,
      hreflang: 'zh',
    },
    {
      href: sitemapConfig.baseUrl,
      hreflang: 'x-default',
    },
  ],

  // 多语言路径映射 - 使用统一配置
  transform: async (config, path) => {
    // 使用统一配置的本地化路径
    const localizedPaths = sitemapConfig.localizedPaths;

    const pathConfig = localizedPaths[path];

    if (pathConfig) {
      // 为本地化路径生成正确的hreflang标签
      return {
        loc: path,
        changefreq: config.changefreq,
        priority: config.priority,
        lastmod: config.autoLastmod ? new Date().toISOString() : undefined,
        alternateRefs: [
          {
            href: `${config.siteUrl}${pathConfig.en}`,
            hreflang: 'en',
          },
          {
            href: `${config.siteUrl}/zh${pathConfig.zh}`,
            hreflang: 'zh',
          },
          {
            href: `${config.siteUrl}${pathConfig.en}`,
            hreflang: 'x-default',
          },
        ],
      };
    }

    // 对于没有本地化配置的路径，返回默认处理
    return {
      loc: path,
      changefreq: config.changefreq,
      priority: config.priority,
      lastmod: config.autoLastmod ? new Date().toISOString() : undefined,
      alternateRefs: config.alternateRefs || [],
    };
  },

  // 添加额外的URL
  additionalPaths: async (config) => {
    const result = [];

    // 添加本地化页面
    const localizedPages = [
      { en: '/about', zh: '/guanyu' },
      { en: '/contact', zh: '/lianxi' },
    ];

    localizedPages.forEach(page => {
      // 添加英文版本
      result.push({
        loc: `${config.siteUrl}${page.en}`,
        changefreq: 'weekly',
        priority: 0.8,
        lastmod: new Date().toISOString(),
        alternateRefs: [
          {
            href: `${config.siteUrl}${page.en}`,
            hreflang: 'en',
          },
          {
            href: `${config.siteUrl}/zh${page.zh}`,
            hreflang: 'zh',
          },
          {
            href: `${config.siteUrl}${page.en}`,
            hreflang: 'x-default',
          },
        ],
      });

      // 添加中文版本
      result.push({
        loc: `${config.siteUrl}/zh${page.zh}`,
        changefreq: 'weekly',
        priority: 0.8,
        lastmod: new Date().toISOString(),
        alternateRefs: [
          {
            href: `${config.siteUrl}${page.en}`,
            hreflang: 'en',
          },
          {
            href: `${config.siteUrl}/zh${page.zh}`,
            hreflang: 'zh',
          },
          {
            href: `${config.siteUrl}${page.en}`,
            hreflang: 'x-default',
          },
        ],
      });
    });

    return result;
  },

  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
      },
    ],
    additionalSitemaps: ['https://tucsenberg.com/server-sitemap.xml'],
  },
};

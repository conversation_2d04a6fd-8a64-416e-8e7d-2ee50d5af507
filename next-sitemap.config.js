/** @type {import('next-sitemap').IConfig} */

module.exports = {
  siteUrl: process.env.SITE_URL || 'https://tucsenberg.com',
  generateRobotsTxt: true,
  exclude: ['/admin/*', '/api/*', '/server-sitemap.xml'],

  // next-intl多语言支持
  alternateRefs: [
    {
      href: process.env.SITE_URL || 'https://tucsenberg.com',
      hreflang: 'en',
    },
    {
      href: `${process.env.SITE_URL || 'https://tucsenberg.com'}/zh`,
      hreflang: 'zh',
    },
    {
      href: process.env.SITE_URL || 'https://tucsenberg.com',
      hreflang: 'x-default',
    },
  ],

  // 多语言路径映射
  transform: async (config, path) => {
    // 本地化路径配置
    const localizedPaths = {
      '/about': { en: '/about', zh: '/guanyu' },
      '/contact': { en: '/contact', zh: '/lianxi' },
      '/blog': { en: '/blog', zh: '/boke' },
      '/products': { en: '/products', zh: '/chanpin' },
      '/services': { en: '/services', zh: '/fuwu' },
      '/pricing': { en: '/pricing', zh: '/jiage' },
      '/support': { en: '/support', zh: '/zhichi' },
      '/privacy': { en: '/privacy', zh: '/yinsi' },
      '/terms': { en: '/terms', zh: '/tiaokuan' },
    };

    const pathConfig = localizedPaths[path];

    if (pathConfig) {
      // 为本地化路径生成正确的hreflang标签
      return {
        loc: path,
        changefreq: config.changefreq,
        priority: config.priority,
        lastmod: config.autoLastmod ? new Date().toISOString() : undefined,
        alternateRefs: [
          {
            href: `${config.siteUrl}${pathConfig.en}`,
            hreflang: 'en',
          },
          {
            href: `${config.siteUrl}/zh${pathConfig.zh}`,
            hreflang: 'zh',
          },
          {
            href: `${config.siteUrl}${pathConfig.en}`,
            hreflang: 'x-default',
          },
        ],
      };
    }

    // 对于没有本地化配置的路径，返回默认处理
    return {
      loc: path,
      changefreq: config.changefreq,
      priority: config.priority,
      lastmod: config.autoLastmod ? new Date().toISOString() : undefined,
      alternateRefs: config.alternateRefs || [],
    };
  },

  // 添加额外的URL
  additionalPaths: async (config) => {
    const result = [];

    // 使用统一配置管理系统 - 临时使用硬编码配置
    const sitemapConfig = {
      localizedPaths: {
        '/about': { en: '/about', zh: '/guanyu' },
        '/contact': { en: '/contact', zh: '/lianxi' },
        '/blog': { en: '/blog', zh: '/boke' },
        '/products': { en: '/products', zh: '/chanpin' },
        '/services': { en: '/services', zh: '/fuwu' },
        '/pricing': { en: '/pricing', zh: '/jiage' },
        '/support': { en: '/support', zh: '/zhichi' },
        '/privacy': { en: '/privacy', zh: '/yinsi' },
        '/terms': { en: '/terms', zh: '/tiaokuan' },
      }
    };

    // 添加本地化页面
    Object.entries(sitemapConfig.localizedPaths).forEach(([enPath, paths]) => {
      // 添加英文版本
      result.push({
        loc: `${config.siteUrl}${paths.en}`,
        changefreq: 'weekly',
        priority: 0.8,
        lastmod: new Date().toISOString(),
        alternateRefs: [
          {
            href: `${config.siteUrl}${paths.en}`,
            hreflang: 'en',
          },
          {
            href: `${config.siteUrl}/zh${paths.zh}`,
            hreflang: 'zh',
          },
          {
            href: `${config.siteUrl}${paths.en}`,
            hreflang: 'x-default',
          },
        ],
      });

      // 添加中文版本
      result.push({
        loc: `${config.siteUrl}/zh${paths.zh}`,
        changefreq: 'weekly',
        priority: 0.8,
        lastmod: new Date().toISOString(),
        alternateRefs: [
          {
            href: `${config.siteUrl}${paths.en}`,
            hreflang: 'en',
          },
          {
            href: `${config.siteUrl}/zh${paths.zh}`,
            hreflang: 'zh',
          },
          {
            href: `${config.siteUrl}${paths.en}`,
            hreflang: 'x-default',
          },
        ],
      });
    });

    return result;
  },

  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
      },
    ],
    additionalSitemaps: ['https://tucsenberg.com/server-sitemap.xml'],
  },
};

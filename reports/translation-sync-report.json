{"timestamp": "2025-07-29T14:02:05.163Z", "summary": {"totalKeys": 102, "syncedKeys": 102, "syncRate": 100, "issueCount": 0, "warningCount": 0}, "issues": [], "warnings": [], "details": {"allKeys": ["accessibility.closeMenu", "accessibility.error", "accessibility.languageSelector", "accessibility.loading", "accessibility.openMenu", "accessibility.skipToContent", "accessibility.themeSelector", "actions.deployNow", "actions.readDocs", "common.cancel", "common.close", "common.confirm", "common.delete", "common.edit", "common.error", "common.filter", "common.loading", "common.next", "common.open", "common.previous", "common.save", "common.search", "common.sort", "common.success", "errors.forbidden", "errors.generic", "errors.loadingFailed", "errors.networkError", "errors.notFound", "errors.serverError", "errors.timeout", "errors.translationMissing", "errors.unauthorized", "errors.validationError", "footer.examples", "footer.goToNextjs", "footer.learn", "formatting.date.lastUpdated", "formatting.date.publishedOn", "formatting.date.today", "formatting.date.tomorrow", "formatting.date.yesterday", "formatting.number.currency", "formatting.number.fileSize", "formatting.number.percentage", "formatting.plurals.items.one", "formatting.plurals.items.other", "formatting.plurals.items.zero", "formatting.plurals.notifications.one", "formatting.plurals.notifications.other", "formatting.plurals.notifications.zero", "formatting.plurals.users.one", "formatting.plurals.users.other", "formatting.plurals.users.zero", "home.description", "home.features.performance.description", "home.features.performance.title", "home.features.scalable.description", "home.features.scalable.title", "home.features.secure.description", "home.features.secure.title", "home.features.title", "home.getStarted", "home.learnMore", "home.subtitle", "home.title", "instructions.getStarted", "instructions.saveChanges", "language.chinese", "language.english", "language.fallback<PERSON><PERSON>ning", "language.switchError", "language.switchSuccess", "language.switching", "language.toggle", "navigation.about", "navigation.blog", "navigation.contact", "navigation.home", "navigation.login", "navigation.logout", "navigation.products", "navigation.profile", "navigation.services", "navigation.settings", "theme.dark", "theme.light", "theme.system", "theme.toggle", "themeDemo.chineseDescription", "themeDemo.chineseInput", "themeDemo.chinesePlaceholder", "themeDemo.chineseTest", "themeDemo.description", "themeDemo.englishDescription", "themeDemo.englishInput", "themeDemo.englishPlaceholder", "themeDemo.englishTest", "themeDemo.outlineButton", "themeDemo.primaryButton", "themeDemo.secondaryButton", "themeDemo.title"], "missingKeys": {}, "inconsistentTypes": {}, "emptyValues": {}, "suspiciousTranslations": {}}}